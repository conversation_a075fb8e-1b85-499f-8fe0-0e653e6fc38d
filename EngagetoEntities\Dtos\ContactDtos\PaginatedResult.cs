﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EngagetoEntities.Entities;

namespace EngagetoEntities.Dtos.ContactDtos
{
    public class PaginatedResult<T>
    {
        public int Total { get; set; }
        public int Page { get; set; }
        public int PerPage { get; set; }
        public List<T> Items { get; set; } = new();
    }

    public class PaginatedTagResult
    {
        public List<Tags> Data { get; set; } = new();
        public int TotalCount { get; set; }
    }
}
